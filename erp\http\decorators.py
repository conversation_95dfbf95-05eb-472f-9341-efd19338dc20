"""
Route decorators for HTTP routes
"""

import functools
import inspect
from typing import Callable, List, Optional, Union

from .metadata import RouteType, HttpMethod, create_route_metadata
from .auth import AuthType, require_auth
from .registries import get_system_route_registry, DatabaseRouteManager
from ..logging import get_logger

logger = get_logger(__name__)


def route(
    path: str,
    *,
    type: RouteType = RouteType.HTTP,
    auth: AuthType = AuthType.USER,
    methods: Optional[List[Union[str, HttpMethod]]] = None,
    cors: Optional[str] = None,
    csrf: bool = True,
    save_session: bool = True,
    database: Optional[str] = None,
    **kwargs
) -> Callable:
    """
    Odoo-style HTTP route decorator
    
    Args:
        path: URL path pattern
        type: Route type (http, json)
        auth: Authentication type (none, public, user, admin)
        methods: HTTP methods (default: ['GET'] for http, ['POST'] for json)
        cors: CORS origin pattern
        csrf: Enable CSRF protection
        save_session: Save session after request
        database: Specific database to register route for (optional)
        **kwargs: Additional route parameters
    
    Returns:
        Decorated function
    
    Example:
        @route('/api/users', type='json', auth='user', methods=['POST'])
        async def get_users(request):
            return {'users': []}
            
        @route('/web/login', type='http', auth='none', methods=['GET', 'POST'])
        async def login_page(request):
            return HTMLResponse('<html>Login</html>')
    """
    
    def decorator(func: Callable) -> Callable:
        # Create route metadata
        route_metadata = create_route_metadata(
            path=path,
            type=type,
            auth=auth,
            methods=methods,
            cors=cors,
            csrf=csrf,
            save_session=save_session,
            database=database,
            original_func=func,
            **kwargs
        )
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            """Route wrapper - simplified for development"""

            # Call the original function directly
            if inspect.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            return result
        
        # Store route metadata on the function for lazy discovery
        wrapper._route_metadata = route_metadata.to_dict()
        
        # Store the handler in the module for discovery by AppRegistry
        # This allows lazy loading to find and register routes when needed
        module = inspect.getmodule(func)
        if module:
            if not hasattr(module, '_route_handlers'):
                module._route_handlers = []
            module._route_handlers.append(wrapper)
            logger.debug(f"Stored route handler for lazy loading: {path}")
        
        return wrapper
    
    return decorator


def systemRoute(
    path: str,
    *,
    methods: Optional[List[Union[str, HttpMethod]]] = None,
    type: RouteType = RouteType.HTTP,
    auth: AuthType = AuthType.USER,
    cors: Optional[str] = None,
    csrf: bool = True,
    save_session: bool = True,
    **kwargs
) -> Callable:
    """
    System route decorator for database-independent routes

    This decorator registers routes with the SystemRouteRegistry for routes that
    don't depend on any specific database (e.g., health checks, system info, etc.)

    Args:
        path: URL path pattern
        methods: HTTP methods (default: ['GET'] for http, ['POST'] for json)
        type: Route type (http, json)
        auth: Authentication type (none, public, user, admin)
        cors: CORS origin pattern
        csrf: Enable CSRF protection
        save_session: Save session after request
        **kwargs: Additional route parameters

    Returns:
        Decorated function

    Example:
        @systemRoute('/health', methods=['GET'])
        async def health_check():
            return {'status': 'healthy'}

        @systemRoute('/api/system/info', type='json', auth='none')
        async def system_info():
            return {'version': '1.0.0'}
    """
    
    def decorator(func: Callable) -> Callable:
        # Create route metadata
        route_metadata = create_route_metadata(
            path=path,
            type=type,
            auth=auth,
            methods=methods,
            cors=cors,
            csrf=csrf,
            save_session=save_session,
            original_func=func,
            **kwargs
        )
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)

        # Register with system route registry immediately
        system_registry = get_system_route_registry()
        # Extract metadata without path since it's passed separately
        metadata_dict = route_metadata.to_dict()
        metadata_dict.pop('path', None)  # Remove path to avoid duplicate
        system_registry.register_route(path, wrapper, **metadata_dict)

        # Store complete metadata on the function (including path for reference)
        wrapper._route_metadata = route_metadata.to_dict()
        wrapper._is_system_route = True

        logger.debug(f"Registered system route: {route_metadata.methods} {path}")

        return wrapper

    return decorator


def _register_route_in_database_registry(path: str, handler: Callable, **kwargs) -> bool:
    """
    Optimized route registration for database-specific registry
    Returns True if successfully scheduled, False otherwise
    """
    return DatabaseRouteManager.register_route_in_database_registry(path, handler, **kwargs)
